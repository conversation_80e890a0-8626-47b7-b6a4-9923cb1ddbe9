# Word文档批注替换功能说明

## 功能概述

`ReplaceService.replace()` 方法实现了以下功能：

1. **读取Word文档** - 使用 `com.aspose.words.Document` 加载指定路径的Word文档
2. **查找批注** - 获取文档中的所有批注节点
3. **替换批注选择的正文内容** - 根据批注内容匹配replacements中的key，用value替换批注选择的正文
4. **删除批注** - 替换完成后删除对应的批注
5. **保存文档** - 保存修改后的文档

## 方法签名

```java
public void replace(String filePath, Map<String, String> replacements) throws Exception
```

### 参数说明

- `filePath`: Word文档的完整文件路径
- `replacements`: 替换映射，其中：
  - `key`: 批注的文本内容
  - `value`: 要替换的正文文本

## 使用示例

### 1. 基本使用

```java
@Autowired
private ReplaceService replaceService;

public void replaceComments() {
    try {
        // 准备替换映射
        Map<String, String> replacements = new HashMap<>();
        replacements.put("批注1", "这是替换后的文本1");
        replacements.put("批注2", "这是替换后的文本2");
        replacements.put("批注3", "这是替换后的文本3");
        
        // 执行替换
        replaceService.replace("D:/documents/test.docx", replacements);
        
        System.out.println("批注替换完成");
        
    } catch (Exception e) {
        System.err.println("批注替换失败: " + e.getMessage());
    }
}
```

### 2. 通过REST API调用

```bash
# 测试接口
GET http://localhost:8080/api/document/replace
```

## 工作原理

### 1. 许可证验证
- 首先验证Aspose Words许可证
- 如果验证失败，会记录警告但继续使用试用版

### 2. 文档加载
- 使用 `Document doc = new Document(filePath)` 加载Word文档

### 3. 批注处理
- 获取文档中所有批注：`doc.getChildNodes(NodeType.COMMENT, true)`
- 遍历每个批注，检查其文本内容是否在替换映射中

### 4. 文本替换
- 查找批注对应的范围开始节点（CommentRangeStart）
- 查找批注对应的范围结束节点（CommentRangeEnd）
- 替换范围内的文本内容
- 删除批注及其范围标记

### 5. 文档保存
- 保存修改后的文档到原路径

## 技术实现细节

### 核心类和方法

1. **ReplaceService.replace()** - 主要入口方法
2. **replaceCommentSelectedText()** - 替换批注选择的文本
3. **findCommentRangeStart()** - 查找批注范围开始节点
4. **findCommentRangeEnd()** - 查找批注范围结束节点
5. **replaceTextBetweenNodes()** - 替换两个节点之间的文本
6. **removeCommentAndRange()** - 删除批注及其范围标记

### 使用的Aspose Words API

- `Document` - 文档对象
- `Comment` - 批注对象
- `CommentRangeStart` - 批注范围开始标记
- `CommentRangeEnd` - 批注范围结束标记
- `NodeType.COMMENT` - 批注节点类型
- `NodeCollection` - 节点集合
- `Run` - 文本运行对象

## 注意事项

1. **文件路径** - 确保提供的文件路径正确且文件存在
2. **批注内容匹配** - 批注文本必须与replacements中的key完全匹配
3. **文档格式** - 支持.docx格式的Word文档
4. **许可证** - 建议使用有效的Aspose Words许可证以避免试用版限制
5. **异常处理** - 方法会抛出Exception，调用时需要适当的异常处理

## 依赖项

- Aspose Words for Java 20.8
- Spring Boot 3.5.0
- Lombok（用于日志记录）

## 日志记录

该功能提供详细的日志记录：

- INFO级别：处理进度和结果
- DEBUG级别：详细的处理步骤
- WARN级别：警告信息（如许可证验证失败）
- ERROR级别：错误信息和异常

## 扩展性

该实现遵循SOLID原则，易于扩展：

- 可以添加更多的文本处理逻辑
- 可以支持更复杂的批注范围处理
- 可以添加不同的文档格式支持
