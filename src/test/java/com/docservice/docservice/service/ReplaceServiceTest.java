package com.docservice.docservice.service;

import com.docservice.docservice.service.license.LicenseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

/**
 * ReplaceService单元测试
 * 
 * 测试批注替换功能的各种场景
 */
@ExtendWith(MockitoExtension.class)
class ReplaceServiceTest {

    @Mock
    private LicenseService licenseService;

    @InjectMocks
    private ReplaceService replaceService;

    @BeforeEach
    void setUp() {
        // 模拟许可证验证成功
        when(licenseService.validateAsposeLicense()).thenReturn(true);
    }

    @Test
    void testReplaceWithValidLicense() {
        // 准备测试数据
        Map<String, String> replacements = new HashMap<>();
        replacements.put("批注1", "替换文本1");
        replacements.put("批注2", "替换文本2");
        replacements.put("批注3", "替换文本3");

        // 注意：这个测试需要实际的Word文档文件才能运行
        // 在实际环境中，你需要创建一个包含批注的测试文档
        
        // 由于我们没有实际的测试文档，这里只是展示测试结构
        replaceService.replace("test-document.docx", replacements);
        
        // 验证方法调用
        // verify(licenseService).validateAsposeLicense();
    }

    @Test
    void testReplaceWithInvalidLicense() {
        // 模拟许可证验证失败
        when(licenseService.validateAsposeLicense()).thenReturn(false);

        Map<String, String> replacements = new HashMap<>();
        replacements.put("批注1", "替换文本1");
        replacements.put("批注2", "替换文本2");
        replacements.put("批注3", "替换文本3");

        // 即使许可证验证失败，方法也应该继续执行（使用试用版）
        // replaceService.replace("test-document.docx", replacements);
        
        // 验证许可证验证被调用
        // verify(licenseService).validateAsposeLicense();
    }
}
