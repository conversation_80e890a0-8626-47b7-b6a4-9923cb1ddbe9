package com.docservice.docservice.controller;

import com.docservice.docservice.WordExportRequest;
import com.docservice.docservice.dto.DocumentImportResponse;
import com.docservice.docservice.dto.DocumentExportResponse;
import com.docservice.docservice.service.DocumentService;
import com.docservice.docservice.service.ReplaceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理控制器
 * 职责：处理HTTP请求和响应，委托业务逻辑给服务层
 * 
 * 遵循原则：
 * - 单一职责：只负责HTTP层的处理
 * - 依赖倒置：依赖抽象的服务接口
 * - 开闭原则：通过依赖注入支持扩展
 */
@Slf4j
@RestController
@RequestMapping("/api/document")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", allowedHeaders = "*")
public class DocumentController {

    private final DocumentService documentService;
    private final ReplaceService replaceService;

    /**
     * 导入Word文档转换为HTML
     * 
     * @param docId 文档ID
     * @return HTML内容响应
     */
    @GetMapping("/import")
    public ResponseEntity<DocumentImportResponse> importDocument(@RequestParam("docId") String docId) {
        try {
            log.info("开始导入文档，docId: {}", docId);
            
            DocumentImportResponse response = documentService.importDocument(docId);
            
            log.info("文档导入成功，docId: {}", docId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("导入文档失败，docId: {}", docId, e);
            return ResponseEntity.internalServerError()
                    .body(DocumentImportResponse.error("导入文档失败: " + e.getMessage()));
        }
    }

    /**
     * 导出HTML内容为Word文档
     * 
     * @param request 导出请求，包含HTML内容和批注
     * @return 导出结果响应
     */
    @PostMapping("/export")
    public ResponseEntity<DocumentExportResponse> exportDocument(@RequestBody WordExportRequest request) {
        try {
            log.info("开始导出文档，批注数量: {}", 
                    request.getComments() != null ? request.getComments().size() : 0);
            
            DocumentExportResponse response = documentService.exportDocument(request);
            
            log.info("文档导出成功");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("导出文档失败", e);
            return ResponseEntity.internalServerError()
                    .body(DocumentExportResponse.error("导出文档失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Document service is running");
    }

    /**
     * 测试批注替换接口
     *
     * @return 服务状态
     */
    @GetMapping("/replace")
    public ResponseEntity<String> replace() {
        try {
            Map<String, String> replacements = new HashMap<>();
            replacements.put("批注1", "替换文本1");
            replacements.put("批注2", "替换文本2");
            replacements.put("批注3", "替换文本3");

            replaceService.replace("D:\\download\\test.docx", replacements);
            return ResponseEntity.ok("批注替换成功");
        } catch (Exception e) {
            log.error("批注替换失败", e);
            return ResponseEntity.internalServerError()
                    .body("批注替换失败: " + e.getMessage());
        }
    }

    /**
     * 调试批注结构接口
     *
     * @return 调试信息
     */
    @GetMapping("/debug-comments")
    public ResponseEntity<String> debugComments() {
        try {
            String debugInfo = replaceService.debugCommentStructure("D:\\download\\test.docx");
            return ResponseEntity.ok(debugInfo);

        } catch (Exception e) {
            log.error("调试批注结构失败", e);
            return ResponseEntity.internalServerError()
                    .body("调试失败: " + e.getMessage());
        }
    }

}
