package com.docservice.docservice.service;

import com.docservice.docservice.WordExportRequest;
import com.docservice.docservice.dto.DocumentImportResponse;
import com.docservice.docservice.dto.DocumentExportResponse;

import java.util.Map;

/**
 * 文档服务接口
 * 职责：定义文档处理的核心业务操作
 * 
 * 遵循原则：
 * - 接口隔离：只定义必要的操作
 * - 依赖倒置：为实现类提供抽象
 */
public interface DocumentService {
    
    /**
     * 导入Word文档并转换为HTML
     * 
     * @param docId 文档ID
     * @return 导入响应，包含HTML内容
     * @throws Exception 处理异常
     */
    DocumentImportResponse importDocument(String docId) throws Exception;
    
    /**
     * 导出HTML内容为Word文档
     * 
     * @param request 导出请求
     * @return 导出响应
     * @throws Exception 处理异常
     */
    DocumentExportResponse exportDocument(WordExportRequest request) throws Exception;

}
