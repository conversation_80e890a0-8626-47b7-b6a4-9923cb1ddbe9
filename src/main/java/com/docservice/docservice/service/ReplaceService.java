package com.docservice.docservice.service;

import com.aspose.words.Comment;
import com.aspose.words.CommentRangeEnd;
import com.aspose.words.CommentRangeStart;
import com.aspose.words.Document;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Range;
import com.docservice.docservice.service.license.LicenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文档替换服务
 * 职责：处理Word文档中批注选择内容的替换操作
 *
 * 遵循原则：
 * - 单一职责：专门处理批注替换逻辑
 * - DRY：避免重复代码
 * - SOLID：依赖抽象，易于扩展
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReplaceService {

    private final LicenseService licenseService;

    /**
     * 替换Word文档中批注选择的正文内容
     *
     * @param filePath 文档文件路径
     * @param replacements 替换映射，key为批注内容，value为要替换的正文文本
     * @throws Exception 处理异常
     */
    public void replace(String filePath, Map<String, String> replacements) throws Exception {
        // 检查许可证
        if (!licenseService.validateAsposeLicense()) {
            log.warn("Aspose许可证验证失败，将使用试用版");
        }

        log.info("开始处理文档替换，文件路径: {}, 替换项数量: {}", filePath, replacements.size());

        // 1. 加载Word文档
        Document doc = new Document(filePath);

        // 2. 获取文档中的所有批注
        NodeCollection comments = doc.getChildNodes(NodeType.COMMENT, true);
        log.info("文档中共找到 {} 个批注", comments.getCount());

        // 3. 收集需要删除的批注（避免在遍历时修改集合）
        List<Comment> commentsToRemove = new ArrayList<>();

        // 4. 遍历所有批注进行处理
        for (int i = 0; i < comments.getCount(); i++) {
            Comment comment = (Comment) comments.get(i);
            String commentText = comment.toString().trim();

            log.debug("处理批注: {}", commentText);

            // 5. 检查批注内容是否在替换映射中
            if (replacements.containsKey(commentText)) {
                String replacementText = replacements.get(commentText);
                log.info("找到匹配的批注: '{}' -> '{}'", commentText, replacementText);

                // 6. 替换批注选择的正文内容
                if (replaceCommentSelectedText(comment, replacementText)) {
                    // 7. 标记批注为待删除
                    commentsToRemove.add(comment);
                    log.info("成功替换批注选择的内容: '{}'", commentText);
                } else {
                    log.warn("替换批注选择的内容失败: '{}'", commentText);
                }
            } else {
                log.debug("批注内容未在替换映射中找到: '{}'", commentText);
            }
        }

        // 8. 删除已处理的批注
        for (Comment comment : commentsToRemove) {
            removeCommentAndRange(comment);
        }

        log.info("成功删除 {} 个批注", commentsToRemove.size());

        // 9. 保存文档
        doc.save(filePath);
        log.info("文档替换处理完成，已保存到: {}", filePath);
    }

    /**
     * 替换批注选择的正文内容
     *
     * @param comment 批注对象
     * @param replacementText 替换文本
     * @return 是否替换成功
     */
    private boolean replaceCommentSelectedText(Comment comment, String replacementText) {
        try {
            // 获取批注的范围开始和结束节点
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);

            if (rangeStart == null || rangeEnd == null) {
                log.warn("无法找到批注的范围标记，批注ID: {}", comment.getId());
                return false;
            }

            // 直接替换批注范围内的文本
            return replaceTextBetweenNodes(rangeStart, rangeEnd, replacementText);

        } catch (Exception e) {
            log.error("替换批注选择文本时发生异常，批注ID: {}", comment.getId(), e);
            return false;
        }
    }

    /**
     * 查找批注对应的范围开始节点
     *
     * @param comment 批注对象
     * @return 范围开始节点
     */
    private CommentRangeStart findCommentRangeStart(Comment comment) {
        try {
            Document doc = (Document) comment.getDocument();
            NodeCollection rangeStarts = doc.getChildNodes(NodeType.COMMENT_RANGE_START, true);

            for (int i = 0; i < rangeStarts.getCount(); i++) {
                CommentRangeStart rangeStart = (CommentRangeStart) rangeStarts.get(i);
                if (rangeStart.getId() == comment.getId()) {
                    return rangeStart;
                }
            }

            log.warn("未找到批注对应的范围开始节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围开始节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }

    /**
     * 查找批注对应的范围结束节点
     *
     * @param comment 批注对象
     * @return 范围结束节点
     */
    private CommentRangeEnd findCommentRangeEnd(Comment comment) {
        try {
            Document doc = comment.getDocument();
            NodeCollection rangeEnds = doc.getChildNodes(NodeType.COMMENT_RANGE_END, true);

            for (int i = 0; i < rangeEnds.getCount(); i++) {
                CommentRangeEnd rangeEnd = (CommentRangeEnd) rangeEnds.get(i);
                if (rangeEnd.getId() == comment.getId()) {
                    return rangeEnd;
                }
            }

            log.warn("未找到批注对应的范围结束节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围结束节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }

    /**
     * 替换两个节点之间的文本内容
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     * @param replacementText 替换文本
     * @return 是否替换成功
     */
    private boolean replaceTextBetweenNodes(Node startNode, Node endNode, String replacementText) {
        try {
            // 获取开始和结束节点之间的所有节点
            List<Node> nodesBetween = getNodesBetween(startNode, endNode);

            // 收集原始文本用于日志
            StringBuilder originalTextBuilder = new StringBuilder();
            for (Node node : nodesBetween) {
                if (node.getNodeType() == NodeType.RUN) {
                    originalTextBuilder.append(node.toString());
                }
            }

            String originalText = originalTextBuilder.toString().trim();
            log.debug("批注选择的原始文本: '{}'", originalText);

            // 删除范围内的所有节点
            for (Node node : nodesBetween) {
                node.remove();
            }

            // 在开始节点后插入新的文本节点
            if (startNode.getParentNode() != null) {
                // 创建新的Run节点包含替换文本
                Document doc = startNode.getDocument();
                com.aspose.words.Run newRun = new com.aspose.words.Run(doc, replacementText);

                // 在开始节点后插入新的Run
                startNode.getParentNode().insertAfter(newRun, startNode);

                log.debug("成功替换文本: '{}' -> '{}'", originalText, replacementText);
                return true;
            } else {
                log.warn("无法找到开始节点的父节点");
                return false;
            }

        } catch (Exception e) {
            log.error("替换节点间文本时发生异常", e);
            return false;
        }
    }

    /**
     * 获取两个节点之间的所有节点
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     * @return 节点列表
     */
    private List<Node> getNodesBetween(Node startNode, Node endNode) {
        List<Node> nodes = new ArrayList<>();

        try {
            Node currentNode = startNode.getNextSibling();

            while (currentNode != null && !currentNode.equals(endNode)) {
                nodes.add(currentNode);
                currentNode = currentNode.getNextSibling();
            }

        } catch (Exception e) {
            log.error("获取节点之间的内容时发生异常", e);
        }

        return nodes;
    }

    /**
     * 删除批注及其范围标记
     *
     * @param comment 要删除的批注
     */
    private void removeCommentAndRange(Comment comment) {
        try {
            // 查找并删除范围开始标记
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            if (rangeStart != null) {
                rangeStart.remove();
                log.debug("已删除批注范围开始标记，批注ID: {}", comment.getId());
            }

            // 查找并删除范围结束标记
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);
            if (rangeEnd != null) {
                rangeEnd.remove();
                log.debug("已删除批注范围结束标记，批注ID: {}", comment.getId());
            }

            // 删除批注本身
            comment.remove();
            log.debug("已删除批注，批注ID: {}", comment.getId());

        } catch (Exception e) {
            log.error("删除批注时发生异常，批注ID: {}", comment.getId(), e);
        }
    }

}