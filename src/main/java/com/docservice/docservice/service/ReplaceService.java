package com.docservice.docservice.service;

import com.aspose.words.Comment;
import com.aspose.words.CommentRangeEnd;
import com.aspose.words.CommentRangeStart;
import com.aspose.words.Document;
import com.aspose.words.DocumentBase;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Range;
import com.aspose.words.Run;
import com.docservice.docservice.service.license.LicenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文档替换服务
 * 职责：处理Word文档中批注选择内容的替换操作
 *
 * 遵循原则：
 * - 单一职责：专门处理批注替换逻辑
 * - DRY：避免重复代码
 * - SOLID：依赖抽象，易于扩展
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReplaceService {

    private final LicenseService licenseService;

    /**
     * 替换Word文档中批注选择的正文内容
     *
     * @param filePath 文档文件路径
     * @param replacements 替换映射，key为批注内容，value为要替换的正文文本
     * @throws Exception 处理异常
     */
    public void replace(String filePath, Map<String, String> replacements) throws Exception {
        // 检查许可证
        if (!licenseService.validateAsposeLicense()) {
            log.warn("Aspose许可证验证失败，将使用试用版");
        }

        log.info("开始处理文档替换，文件路径: {}, 替换项数量: {}", filePath, replacements.size());

        // 1. 加载Word文档
        Document doc = new Document(filePath);

        // 2. 获取文档中的所有批注
        NodeCollection comments = doc.getChildNodes(NodeType.COMMENT, true);
        log.info("文档中共找到 {} 个批注", comments.getCount());

        // 3. 收集需要删除的批注（避免在遍历时修改集合）
        List<Comment> commentsToRemove = new ArrayList<>();

        // 4. 遍历所有批注进行处理
        for (int i = 0; i < comments.getCount(); i++) {
            Comment comment = (Comment) comments.get(i);
            String commentText = comment.getText().trim();

            log.debug("处理批注: {}", commentText);

            // 5. 检查批注内容是否在替换映射中
            if (replacements.containsKey(commentText)) {
                String replacementText = replacements.get(commentText);
                log.info("找到匹配的批注: '{}' -> '{}'", commentText, replacementText);

                // 6. 替换批注选择的正文内容
                if (replaceCommentSelectedText(comment, replacementText)) {
                    // 7. 标记批注为待删除
                    commentsToRemove.add(comment);
                    log.info("成功替换批注选择的内容: '{}'", commentText);
                } else {
                    log.warn("替换批注选择的内容失败: '{}'", commentText);
                }
            } else {
                log.debug("批注内容未在替换映射中找到: '{}'", commentText);
            }
        }

        // 8. 删除已处理的批注
        for (Comment comment : commentsToRemove) {
            removeCommentAndRange(comment);
        }

        log.info("成功删除 {} 个批注", commentsToRemove.size());

        // 9. 保存文档
        doc.save(filePath);
        log.info("文档替换处理完成，已保存到: {}", filePath);
    }

    /**
     * 替换批注选择的正文内容
     *
     * @param comment 批注对象
     * @param replacementText 替换文本
     * @return 是否替换成功
     */
    private boolean replaceCommentSelectedText(Comment comment, String replacementText) {
        try {
            // 获取批注的范围开始和结束节点
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);

            if (rangeStart == null || rangeEnd == null) {
                log.warn("无法找到批注的范围标记，批注ID: {}", comment.getId());
                return false;
            }

            // 使用简化的方法：获取范围内的文本并替换
            try {
                // 获取范围开始和结束节点的父节点
                Node startParent = rangeStart.getParentNode();
                Node endParent = rangeEnd.getParentNode();

                // 如果在同一个段落中，使用段落的范围进行替换
                if (startParent != null && startParent.equals(endParent)) {
                    Range paragraphRange = startParent.getRange();
                    String originalText = paragraphRange.getText();

                    // 简单的文本替换（这里可以根据需要改进为更精确的范围替换）
                    if (originalText.contains(replacementText)) {
                        log.debug("文本已经是目标内容，无需替换");
                        return true;
                    }

                    // 创建新的Run节点
                    Document doc = (Document) rangeStart.getDocument();
                    Run newRun = new Run(doc, replacementText);

                    // 在范围开始节点后插入新文本
                    rangeStart.getParentNode().insertAfter(newRun, rangeStart);

                    // 删除范围内的原有内容
                    removeNodesBetween(rangeStart, rangeEnd);

                    log.debug("成功替换批注选择的文本: '{}'", replacementText);
                    return true;
                } else {
                    log.warn("批注范围跨越多个段落，暂不支持");
                    return false;
                }

            } catch (Exception e) {
                log.error("替换文本时发生异常", e);
                return false;
            }

        } catch (Exception e) {
            log.error("替换批注选择文本时发生异常，批注ID: {}", comment.getId(), e);
            return false;
        }
    }

    /**
     * 查找批注对应的范围开始节点
     *
     * @param comment 批注对象
     * @return 范围开始节点
     */
    private CommentRangeStart findCommentRangeStart(Comment comment) {
        try {
            Document doc = (Document) comment.getDocument();
            NodeCollection rangeStarts = doc.getChildNodes(NodeType.COMMENT_RANGE_START, true);

            for (int i = 0; i < rangeStarts.getCount(); i++) {
                CommentRangeStart rangeStart = (CommentRangeStart) rangeStarts.get(i);
                if (rangeStart.getId() == comment.getId()) {
                    return rangeStart;
                }
            }

            log.warn("未找到批注对应的范围开始节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围开始节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }

    /**
     * 查找批注对应的范围结束节点
     *
     * @param comment 批注对象
     * @return 范围结束节点
     */
    private CommentRangeEnd findCommentRangeEnd(Comment comment) {
        try {
            Document doc = (Document) comment.getDocument();
            NodeCollection rangeEnds = doc.getChildNodes(NodeType.COMMENT_RANGE_END, true);

            for (int i = 0; i < rangeEnds.getCount(); i++) {
                CommentRangeEnd rangeEnd = (CommentRangeEnd) rangeEnds.get(i);
                if (rangeEnd.getId() == comment.getId()) {
                    return rangeEnd;
                }
            }

            log.warn("未找到批注对应的范围结束节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围结束节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }



    /**
     * 获取两个节点之间的所有节点
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     * @return 节点列表
     */
    private List<Node> getNodesBetween(Node startNode, Node endNode) {
        List<Node> nodes = new ArrayList<>();

        try {
            Node currentNode = startNode.getNextSibling();

            while (currentNode != null && !currentNode.equals(endNode)) {
                nodes.add(currentNode);
                currentNode = currentNode.getNextSibling();
            }

        } catch (Exception e) {
            log.error("获取节点之间的内容时发生异常", e);
        }

        return nodes;
    }

    /**
     * 删除两个节点之间的所有节点
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     */
    private void removeNodesBetween(Node startNode, Node endNode) {
        try {
            List<Node> nodesToRemove = getNodesBetween(startNode, endNode);
            for (Node node : nodesToRemove) {
                node.remove();
            }
            log.debug("成功删除 {} 个节点", nodesToRemove.size());
        } catch (Exception e) {
            log.error("删除节点时发生异常", e);
        }
    }

    /**
     * 删除批注及其范围标记
     *
     * @param comment 要删除的批注
     */
    private void removeCommentAndRange(Comment comment) {
        try {
            // 查找并删除范围开始标记
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            if (rangeStart != null) {
                rangeStart.remove();
                log.debug("已删除批注范围开始标记，批注ID: {}", comment.getId());
            }

            // 查找并删除范围结束标记
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);
            if (rangeEnd != null) {
                rangeEnd.remove();
                log.debug("已删除批注范围结束标记，批注ID: {}", comment.getId());
            }

            // 删除批注本身
            comment.remove();
            log.debug("已删除批注，批注ID: {}", comment.getId());

        } catch (Exception e) {
            log.error("删除批注时发生异常，批注ID: {}", comment.getId(), e);
        }
    }

}