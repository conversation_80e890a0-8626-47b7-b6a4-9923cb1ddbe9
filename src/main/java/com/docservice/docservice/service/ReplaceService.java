package com.docservice.docservice.service;

import com.aspose.words.Comment;
import com.aspose.words.CommentRangeEnd;
import com.aspose.words.CommentRangeStart;
import com.aspose.words.Document;
import com.aspose.words.DocumentBase;
import com.aspose.words.Node;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.Range;
import com.aspose.words.Run;
import com.docservice.docservice.service.license.LicenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文档替换服务
 * 职责：处理Word文档中批注选择内容的替换操作
 *
 * 遵循原则：
 * - 单一职责：专门处理批注替换逻辑
 * - DRY：避免重复代码
 * - SOLID：依赖抽象，易于扩展
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReplaceService {

    private final LicenseService licenseService;

    /**
     * 替换Word文档中批注选择的正文内容
     *
     * @param filePath 文档文件路径
     * @param replacements 替换映射，key为批注内容，value为要替换的正文文本
     * @throws Exception 处理异常
     */
    public void replace(String filePath, Map<String, String> replacements) throws Exception {
        // 检查许可证
        if (!licenseService.validateAsposeLicense()) {
            log.warn("Aspose许可证验证失败，将使用试用版");
        }

        log.info("开始处理文档替换，文件路径: {}, 替换项数量: {}", filePath, replacements.size());

        // 1. 加载Word文档
        Document doc = new Document(filePath);

        // 2. 获取文档中的所有批注
        NodeCollection comments = doc.getChildNodes(NodeType.COMMENT, true);
        log.info("文档中共找到 {} 个批注", comments.getCount());

        // 3. 收集需要删除的批注（避免在遍历时修改集合）
        List<Comment> commentsToRemove = new ArrayList<>();

        // 4. 遍历所有批注进行处理
        for (int i = 0; i < comments.getCount(); i++) {
            Comment comment = (Comment) comments.get(i);
            String commentText = comment.getText().trim();

            log.debug("处理批注 {}: ID={}, 内容='{}'", i, comment.getId(), commentText);

            // 5. 检查批注内容是否在替换映射中
            if (replacements.containsKey(commentText)) {
                String replacementText = replacements.get(commentText);
                log.info("找到匹配的批注: '{}' -> '{}', 批注ID: {}", commentText, replacementText, comment.getId());

                // 6. 替换批注选择的正文内容
                if (replaceCommentSelectedText(comment, replacementText)) {
                    // 7. 标记批注为待删除
                    commentsToRemove.add(comment);
                    log.info("成功替换批注选择的内容: '{}', 批注ID: {}", commentText, comment.getId());
                } else {
                    log.warn("替换批注选择的内容失败: '{}', 批注ID: {}", commentText, comment.getId());
                }
            } else {
                log.debug("批注内容未在替换映射中找到: '{}', 批注ID: {}", commentText, comment.getId());
            }
        }

        // 8. 删除已处理的批注
        for (Comment comment : commentsToRemove) {
            removeCommentAndRange(comment);
        }

        log.info("成功删除 {} 个批注", commentsToRemove.size());

        // 9. 保存文档
        doc.save(filePath);
        log.info("文档替换处理完成，已保存到: {}", filePath);
    }

    /**
     * 替换批注选择的正文内容
     *
     * @param comment 批注对象
     * @param replacementText 替换文本
     * @return 是否替换成功
     */
    private boolean replaceCommentSelectedText(Comment comment, String replacementText) {
        try {
            // 获取批注的范围开始和结束节点
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);

            if (rangeStart == null || rangeEnd == null) {
                log.warn("无法找到批注的范围标记，批注ID: {}，尝试使用备用方案", comment.getId());
                return replaceCommentSelectedTextFallback(comment, replacementText);
            }

            // 使用简化的方法：获取范围内的文本并替换
            try {
                // 获取范围开始和结束节点的父节点
                Node startParent = rangeStart.getParentNode();
                Node endParent = rangeEnd.getParentNode();

                // 如果在同一个段落中，使用段落的范围进行替换
                if (startParent != null && startParent.equals(endParent)) {
                    Range paragraphRange = startParent.getRange();
                    String originalText = paragraphRange.getText();

                    // 简单的文本替换（这里可以根据需要改进为更精确的范围替换）
                    if (originalText.contains(replacementText)) {
                        log.debug("文本已经是目标内容，无需替换");
                        return true;
                    }

                    // 创建新的Run节点
                    Document doc = (Document) rangeStart.getDocument();
                    Run newRun = new Run(doc, replacementText);

                    // 在范围开始节点后插入新文本
                    rangeStart.getParentNode().insertAfter(newRun, rangeStart);

                    // 删除范围内的原有内容
                    removeNodesBetween(rangeStart, rangeEnd);

                    log.debug("成功替换批注选择的文本: '{}'", replacementText);
                    return true;
                } else {
                    log.warn("批注范围跨越多个段落，暂不支持");
                    return false;
                }

            } catch (Exception e) {
                log.error("替换文本时发生异常", e);
                return false;
            }

        } catch (Exception e) {
            log.error("替换批注选择文本时发生异常，批注ID: {}", comment.getId(), e);
            return false;
        }
    }

    /**
     * 备用方案：当无法找到批注范围标记时，使用智能文本替换
     *
     * @param comment 批注对象
     * @param replacementText 替换文本
     * @return 是否替换成功
     */
    private boolean replaceCommentSelectedTextFallback(Comment comment, String replacementText) {
        try {
            log.info("使用备用方案处理批注，ID: {}", comment.getId());

            // 方案1: 尝试在整个文档中查找并替换可能的文本
            Document doc = (Document) comment.getDocument();

            // 获取批注所在的段落
            Node parentParagraph = comment.getAncestor(NodeType.PARAGRAPH);
            if (parentParagraph != null) {
                Range paragraphRange = parentParagraph.getRange();
                String paragraphText = paragraphRange.getText();

                log.debug("段落原始文本: '{}'", paragraphText.trim());

                // 方案1a: 尝试基于批注内容推测被选择的文本
                String commentText = comment.getText().trim();

                // 常见的批注模式：批注内容可能包含被选择的文本
                // 例如：批注内容为"批注1"，可能选择的文本也是"批注1"或相关文本
                if (paragraphText.contains(commentText)) {
                    // 在段落范围内替换第一个匹配的文本
                    String newParagraphText = paragraphText.replaceFirst(commentText, replacementText);

                    // 清空段落内容并重新填充
                    clearParagraphContent(parentParagraph);
                    Run newRun = new Run(doc, newParagraphText);
                    parentParagraph.appendChild(newRun);

                    log.debug("备用方案1a成功：基于批注内容替换文本 '{}' -> '{}'", commentText, replacementText);
                    return true;
                }

                // 方案1b: 查找段落中可能的占位符文本
                String[] possiblePlaceholders = {
                    "批注1", "批注2", "批注3",
                    "placeholder1", "placeholder2", "placeholder3",
                    "[批注1]", "[批注2]", "[批注3]",
                    "{{批注1}}", "{{批注2}}", "{{批注3}}"
                };

                for (String placeholder : possiblePlaceholders) {
                    if (paragraphText.contains(placeholder) && commentText.contains(placeholder.replaceAll("[\\[\\]{}]", ""))) {
                        String newParagraphText = paragraphText.replaceFirst(placeholder, replacementText);

                        clearParagraphContent(parentParagraph);
                        Run newRun = new Run(doc, newParagraphText);
                        parentParagraph.appendChild(newRun);

                        log.debug("备用方案1b成功：替换占位符 '{}' -> '{}'", placeholder, replacementText);
                        return true;
                    }
                }
            }

            // 方案2: 在批注位置直接插入替换文本（作为最后的备选）
            Run newRun = new Run(doc, "[" + replacementText + "]");
            comment.getParentNode().insertAfter(newRun, comment);

            log.debug("备用方案2成功：在批注位置插入替换文本: '[{}]'", replacementText);
            return true;

        } catch (Exception e) {
            log.error("备用方案处理批注时发生异常，批注ID: {}", comment.getId(), e);
            return false;
        }
    }

    /**
     * 清空段落内容，保留段落结构
     *
     * @param paragraph 段落节点
     */
    private void clearParagraphContent(Node paragraph) {
        try {
            // 获取段落的所有子节点
            NodeCollection childNodes = paragraph.getChildNodes(NodeType.ANY, false);

            // 从后往前删除，避免索引问题
            for (int i = childNodes.getCount() - 1; i >= 0; i--) {
                Node child = childNodes.get(i);
                // 只删除Run节点，保留其他格式节点
                if (child.getNodeType() == NodeType.RUN) {
                    child.remove();
                }
            }

            log.debug("成功清空段落内容");

        } catch (Exception e) {
            log.error("清空段落内容时发生异常", e);
        }
    }

    /**
     * 查找批注对应的范围开始节点
     *
     * @param comment 批注对象
     * @return 范围开始节点
     */
    private CommentRangeStart findCommentRangeStart(Comment comment) {
        try {
            Document doc = (Document) comment.getDocument();
            NodeCollection rangeStarts = doc.getChildNodes(NodeType.COMMENT_RANGE_START, true);

            log.debug("查找批注范围开始节点，批注ID: {}, 总共有 {} 个范围开始节点",
                    comment.getId(), rangeStarts.getCount());

            for (int i = 0; i < rangeStarts.getCount(); i++) {
                CommentRangeStart rangeStart = (CommentRangeStart) rangeStarts.get(i);
                log.debug("检查范围开始节点 {}: ID = {}", i, rangeStart.getId());

                // 使用equals方法比较ID，而不是==
                if (Integer.valueOf(rangeStart.getId()).equals(Integer.valueOf(comment.getId()))) {
                    log.debug("找到匹配的范围开始节点，ID: {}", rangeStart.getId());
                    return rangeStart;
                }
            }

            log.warn("未找到批注对应的范围开始节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围开始节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }

    /**
     * 查找批注对应的范围结束节点
     *
     * @param comment 批注对象
     * @return 范围结束节点
     */
    private CommentRangeEnd findCommentRangeEnd(Comment comment) {
        try {
            Document doc = (Document) comment.getDocument();
            NodeCollection rangeEnds = doc.getChildNodes(NodeType.COMMENT_RANGE_END, true);

            log.debug("查找批注范围结束节点，批注ID: {}, 总共有 {} 个范围结束节点",
                    comment.getId(), rangeEnds.getCount());

            for (int i = 0; i < rangeEnds.getCount(); i++) {
                CommentRangeEnd rangeEnd = (CommentRangeEnd) rangeEnds.get(i);
                log.debug("检查范围结束节点 {}: ID = {}", i, rangeEnd.getId());

                // 使用equals方法比较ID，而不是==
                if (Integer.valueOf(rangeEnd.getId()).equals(Integer.valueOf(comment.getId()))) {
                    log.debug("找到匹配的范围结束节点，ID: {}", rangeEnd.getId());
                    return rangeEnd;
                }
            }

            log.warn("未找到批注对应的范围结束节点，批注ID: {}", comment.getId());
            return null;

        } catch (Exception e) {
            log.error("查找批注范围结束节点时发生异常，批注ID: {}", comment.getId(), e);
            return null;
        }
    }



    /**
     * 获取两个节点之间的所有节点
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     * @return 节点列表
     */
    private List<Node> getNodesBetween(Node startNode, Node endNode) {
        List<Node> nodes = new ArrayList<>();

        try {
            Node currentNode = startNode.getNextSibling();

            while (currentNode != null && !currentNode.equals(endNode)) {
                nodes.add(currentNode);
                currentNode = currentNode.getNextSibling();
            }

        } catch (Exception e) {
            log.error("获取节点之间的内容时发生异常", e);
        }

        return nodes;
    }

    /**
     * 删除两个节点之间的所有节点
     *
     * @param startNode 开始节点
     * @param endNode 结束节点
     */
    private void removeNodesBetween(Node startNode, Node endNode) {
        try {
            List<Node> nodesToRemove = getNodesBetween(startNode, endNode);
            for (Node node : nodesToRemove) {
                node.remove();
            }
            log.debug("成功删除 {} 个节点", nodesToRemove.size());
        } catch (Exception e) {
            log.error("删除节点时发生异常", e);
        }
    }

    /**
     * 删除批注及其范围标记
     *
     * @param comment 要删除的批注
     */
    private void removeCommentAndRange(Comment comment) {
        try {
            // 查找并删除范围开始标记
            CommentRangeStart rangeStart = findCommentRangeStart(comment);
            if (rangeStart != null) {
                rangeStart.remove();
                log.debug("已删除批注范围开始标记，批注ID: {}", comment.getId());
            }

            // 查找并删除范围结束标记
            CommentRangeEnd rangeEnd = findCommentRangeEnd(comment);
            if (rangeEnd != null) {
                rangeEnd.remove();
                log.debug("已删除批注范围结束标记，批注ID: {}", comment.getId());
            }

            // 删除批注本身
            comment.remove();
            log.debug("已删除批注，批注ID: {}", comment.getId());

        } catch (Exception e) {
            log.error("删除批注时发生异常，批注ID: {}", comment.getId(), e);
        }
    }

    /**
     * 调试批注结构，用于诊断问题
     *
     * @param filePath 文档文件路径
     * @return 调试信息
     * @throws Exception 处理异常
     */
    public String debugCommentStructure(String filePath) throws Exception {
        StringBuilder debugInfo = new StringBuilder();

        // 检查许可证
        if (!licenseService.validateAsposeLicense()) {
            debugInfo.append("警告: Aspose许可证验证失败，使用试用版\n");
        }

        // 加载文档
        Document doc = new Document(filePath);
        debugInfo.append("文档加载成功: ").append(filePath).append("\n\n");

        // 获取所有批注
        NodeCollection comments = doc.getChildNodes(NodeType.COMMENT, true);
        debugInfo.append("批注总数: ").append(comments.getCount()).append("\n");

        // 获取所有范围开始节点
        NodeCollection rangeStarts = doc.getChildNodes(NodeType.COMMENT_RANGE_START, true);
        debugInfo.append("批注范围开始节点总数: ").append(rangeStarts.getCount()).append("\n");

        // 获取所有范围结束节点
        NodeCollection rangeEnds = doc.getChildNodes(NodeType.COMMENT_RANGE_END, true);
        debugInfo.append("批注范围结束节点总数: ").append(rangeEnds.getCount()).append("\n\n");

        // 详细分析每个批注
        for (int i = 0; i < comments.getCount(); i++) {
            Comment comment = (Comment) comments.get(i);
            debugInfo.append("=== 批注 ").append(i + 1).append(" ===\n");
            debugInfo.append("ID: ").append(comment.getId()).append("\n");
            debugInfo.append("作者: ").append(comment.getAuthor()).append("\n");
            debugInfo.append("内容: '").append(comment.getText().trim()).append("'\n");
            debugInfo.append("日期: ").append(comment.getDateTime()).append("\n");

            // 查找对应的范围标记
            CommentRangeStart rangeStart = null;
            CommentRangeEnd rangeEnd = null;

            for (int j = 0; j < rangeStarts.getCount(); j++) {
                CommentRangeStart rs = (CommentRangeStart) rangeStarts.get(j);
                if (rs.getId() == comment.getId()) {
                    rangeStart = rs;
                    break;
                }
            }

            for (int j = 0; j < rangeEnds.getCount(); j++) {
                CommentRangeEnd re = (CommentRangeEnd) rangeEnds.get(j);
                if (re.getId() == comment.getId()) {
                    rangeEnd = re;
                    break;
                }
            }

            debugInfo.append("范围开始节点: ").append(rangeStart != null ? "找到" : "未找到").append("\n");
            debugInfo.append("范围结束节点: ").append(rangeEnd != null ? "找到" : "未找到").append("\n");

            if (rangeStart != null && rangeEnd != null) {
                try {
                    Node startParent = rangeStart.getParentNode();
                    Node endParent = rangeEnd.getParentNode();
                    debugInfo.append("范围在同一段落: ").append(startParent.equals(endParent) ? "是" : "否").append("\n");

                    if (startParent.equals(endParent)) {
                        Range range = startParent.getRange();
                        debugInfo.append("段落文本: '").append(range.getText().trim()).append("'\n");
                    }
                } catch (Exception e) {
                    debugInfo.append("分析范围时出错: ").append(e.getMessage()).append("\n");
                }
            }

            debugInfo.append("\n");
        }

        // 列出所有范围开始节点的ID
        debugInfo.append("=== 所有范围开始节点ID ===\n");
        for (int i = 0; i < rangeStarts.getCount(); i++) {
            CommentRangeStart rs = (CommentRangeStart) rangeStarts.get(i);
            debugInfo.append("节点 ").append(i).append(": ID = ").append(rs.getId()).append("\n");
        }

        // 列出所有范围结束节点的ID
        debugInfo.append("\n=== 所有范围结束节点ID ===\n");
        for (int i = 0; i < rangeEnds.getCount(); i++) {
            CommentRangeEnd re = (CommentRangeEnd) rangeEnds.get(i);
            debugInfo.append("节点 ").append(i).append(": ID = ").append(re.getId()).append("\n");
        }

        return debugInfo.toString();
    }

}