package com.docservice.docservice.service.impl;

import com.docservice.docservice.CommentData;
import com.docservice.docservice.WordExportRequest;
import com.docservice.docservice.dto.DocumentImportResponse;
import com.docservice.docservice.dto.DocumentExportResponse;
import com.docservice.docservice.service.DocumentService;
import com.docservice.docservice.service.conversion.DocumentConversionService;
import com.docservice.docservice.service.comment.CommentService;
import com.docservice.docservice.service.file.FileService;
import com.docservice.docservice.util.ValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文档服务实现类
 * 职责：协调各个专门服务完成文档处理业务
 * 
 * 遵循原则：
 * - 单一职责：只负责业务流程编排
 * - 依赖倒置：依赖抽象服务接口
 * - 组合优于继承：通过组合各种服务实现功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentServiceImpl implements DocumentService {

    private final DocumentConversionService conversionService;
    private final CommentService commentService;
    private final FileService fileService;

    @Override
    public DocumentImportResponse importDocument(String docId) throws Exception {
        log.info("开始导入文档处理流程，docId: {}", docId);
        
        // 1. 验证输入
        ValidationUtils.validateDocId(docId);
        
        // 2. 构建文件路径
        String inputPath = fileService.buildInputPath(docId);
        
        // 3. 验证文件存在性
        fileService.validateFileExists(inputPath);
        
        // 4. 转换Word到HTML
        String htmlContent = conversionService.convertWordToHtml(inputPath);
        
        // 5. 清理HTML内容
        String cleanedHtml = conversionService.cleanHtml(htmlContent);
        
        // 6. 保存清理后的HTML（可选，用于调试）
        String outputPath = fileService.buildOutputPath(docId, "html");
        fileService.saveContent(outputPath, cleanedHtml);
        
        log.info("文档导入处理完成，docId: {}", docId);
        return DocumentImportResponse.success(cleanedHtml);
    }

    @Override
    public DocumentExportResponse exportDocument(WordExportRequest request) throws Exception {
        log.info("开始导出文档处理流程");
        
        // 1. 验证请求
        ValidationUtils.validateExportRequest(request);
        
        // 2. 获取请求数据
        String htmlContent = request.getContent();
        List<CommentData> comments = request.getComments();
        
        // 3. 生成临时文件路径
        String tempPath = fileService.generateTempPath("temp_export.docx");
        String outputPath = fileService.buildOutputPath("export", "docx");
        
        try {
            // 4. HTML转Word
            conversionService.convertHtmlToWord(htmlContent, tempPath);
            
            // 5. 添加批注
            commentService.addCommentsToDocument(tempPath, outputPath, comments);
            
            // 6. 验证输出文件
            fileService.validateFileExists(outputPath);
            
            log.info("文档导出处理完成，输出文件: {}", outputPath);
            return DocumentExportResponse.success(outputPath);
            
        } finally {
            // 7. 清理临时文件
            fileService.cleanupTempFile(tempPath);
        }
    }

}
